import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
//  import {defaultFormValue, FormTimKiemDanhMucDaiLy, LOAI_DAI_LY_TK, TRANG_THAI_CHI_TIET_DAI_LY} from "../index.configs";
import {useBaoHiemTaiSanContext} from "../../index.context";
import "../../index.default.scss";
//  import {DaiLyChaProps, DataIndexDoiTuongDS, IModalTimDaiLyChaRef, TableDaiLyChaColumnDataIndex} from "./index.configs";
import {
  TableDoiTuongDataType,
  tableDoiTuongDSColumn,
  IModalDanhSachDoiTuongTaiSanRef,
  DanhSachDoiTuongProps,
  FormTimKiemDanhSachDoiTuong,
  defaultFormValueTimKiemDoiTuongTaiSan,
  DataIndexDoiTuongDS,
  IModalchiTietDoiTuongRef,
} from "./Constant";
import {ModalchiTietDoiTuong} from "./ModalChiTietDoiTuong";
const {ten} = FormTimKiemDanhSachDoiTuong;
const ModalDanhSachDoiTuongComponent = forwardRef<IModalDanhSachDoiTuongTaiSanRef, DanhSachDoiTuongProps>(({onSelectDoiTuong}: DanhSachDoiTuongProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);

      initData();
    },
    close: () => setIsOpen(false),
  }));
  const initData = async () => {
    try {
      // await layDanhSachDaiLyPhanTrang;
      await onSearchApi(defaultFormValueTimKiemDoiTuongTaiSan);
    } catch (error) {
      console.log("error", error);
    }
  };
  const {timKiemPhanTrangDanhSachDoiTuong, tongSoDongDoiTuong, danhSachDoiTuong, layChiTietDoiTuong, loading, listDoiTac} = useBaoHiemTaiSanContext();

  const [doiTuongSelected, setDoiTuongSelected] = useState<CommonExecute.Execute.IDoiTuong | null>(null);
  // const [tongSoDongDaiLyCha, setTongSoDongDaiLyCha] = useState<number>(0);
  //    const [danhSachDaiLy, setDanhSachDaiLy] = useState<Array<CommonExecute.Execute.IDanhMucDaiLy>>([]);

  const [IsOpen, setIsOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams>(defaultFormValueTimKiemDoiTuongTaiSan);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndexDoiTuongDS | "">("");
  const searchInput = useRef<InputRef>(null);
  useRef(() => {
    if (doiTuongSelected && doiTuongSelected.ma) {
    }
  });
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDoiTuongDS) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback((clearFilters: () => void, confirm: () => void, dataIndex: DataIndexDoiTuongDS) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  }, []);
  const refModalChiTietDoiTuong = useRef<IModalchiTietDoiTuongRef>(null);
  const dataTableListDoiTuong = useMemo<Array<TableDoiTuongDataType>>(() => {
    try {
      const tableData = danhSachDoiTuong.map((item: any, index: number) => {
        return {
          // stt: item.stt ?? index + 1,
          stt: item.stt,
          sott: item.sott,
          ma: item.ma,
          ten: item.ten,
          tinh_thanh: item.tinh_thanh,
          phuong_xa: item.phuong_xa,
          ten_tinh: item.ten_tinh,
          ten_phuong_xa: item.ten_phuong_xa,
          dchi: item.dchi,
          latitude: item.latitude,
          longitude: item.longitude,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableDoiTuongDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDaiLy error", error);
      return [];
    }
  }, [danhSachDoiTuong]);

  //Bấm tìm kiếm
  const onSearchApi = async (values: ReactQuery.ITimKiemPhanTrangDoiTuongParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,

      ten: values.ten ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? pageSize,
    };
    console.log("cleanedValues", cleanedValues);
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    const result = await timKiemPhanTrangDanhSachDoiTuong({...cleanedValues, trang: 1, so_dong: pageSize});
    //  if (result) {
    //    setDanhSachDaiLy(result.data);
    //    setTongSoDongDaiLy(result.tong_so_dong);
    //  } else {
    //    setDanhSachDaiLy([]);
    //    setTongSoDongDaiLy(0);
    //  }
    //  console.log("danh sách đại lý ", result?.data);
  };
  //Bấm xác nhận chọn
  const onPressXacNhan = () => {
    try {
      if (doiTuongSelected) {
        console.log("doiTuongSelected", doiTuongSelected);

        onSelectDoiTuong(doiTuongSelected);
      } else {
        onSelectDoiTuong(null);
      }
      setIsOpen(false);
    } catch (error) {
      console.log("onPressXacNhan error", error);
    }
  };
  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    async (page: number, pageSize: number) => {
      try {
        setPage(page);
        setPageSize(pageSize);
        const result = await timKiemPhanTrangDanhSachDoiTuong({...searchParams, trang: page, so_dong: pageSize});
        //  if (result) {
        //    setDanhSachDaiLy(result.data);
        //    setTongSoDongDaiLy(result.tong_so_dong);
        //  } else {
        //    setDanhSachDaiLy([]);
        //    setTongSoDongDaiLy(0);
        //  }
      } catch (error) {
        console.log("onChangePage error:", error);
        //  setDanhSachDaiLy([]);
        //  setTongSoDongDaiLy(0);
      }
    },
    [searchParams],
  ); //tạo cột tìm kiếm
  //   const daiLyChaSelected = useRef<CommonExecute.Execute.IDanhMucDaiLy | null>(null);
  const getColumnSearchProps = (dataIndex: DataIndexDoiTuongDS, title: string): TableColumnType<TableDoiTuongDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="header-cell-custom flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    render: (text, record, index) => {
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderTable = () => {
    let clickTimmer: NodeJS.Timeout;
    return (
      <Table<TableDoiTuongDataType>
        {...defaultTableProps}
        // bordered={false}
        // sticky
        className="Dai-ly-cha no-header-border-radius"
        // scroll={{x: "max-content,y: 100vh"}}
        loading={loading}
        // style={{cursor: "pointer"}}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: async event => {
              if (clickTimmer) clearTimeout(clickTimmer);
              clickTimmer = setTimeout(async () => {
                if (record.key.toString().includes("empty")) return;
                const chiTietDoiTuong = await layChiTietDoiTuong({ma: record.ma});
                console.log("chiTietDoiTuong", chiTietDoiTuong);
                if (chiTietDoiTuong) {
                  refModalChiTietDoiTuong.current?.open(chiTietDoiTuong);
                }
              }, 250);
            },
            onDoubleClick: () => {
              console.log("record double click", record);
              if (clickTimmer) clearTimeout(clickTimmer);

              // Truyền trực tiếp record thay vì dùng state
              if (record && !record.key.toString().includes("empty")) {
                onSelectDoiTuong(record as CommonExecute.Execute.IDoiTuong);
                setDoiTuongSelected(record as CommonExecute.Execute.IDoiTuong);
                setIsOpen(false);
              }
            },
            //  onPressXacNhan(),
          };
        }}
        columns={(tableDoiTuongDSColumn || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableDoiTuongDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        rowClassName={record => (record.ma && record.ma === doiTuongSelected?.ma ? "custom-row-selected" : "")}
        dataSource={dataTableListDoiTuong}
        // title={renderHeaderTableDanhMucDaiLyCha}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDongDoiTuong,
          defaultPageSize: pageSize,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 6) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableDanhSachDoiTuong = () => {
    return (
      <div
        style={{
          margin: "16px 0",
        }}>
        <Form layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum(ten)}
              <Col span={3}>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                  Tìm kiếm
                </Button>
              </Col>
              <Col span={3}>
                <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietDoiTuong.current?.open()} loading={loading} className="mb-0 w-full">
                  Thêm mới
                </Button>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  // const renderFooter = () => {
  //   return (
  //     <Form.Item>
  //       <Tooltip title={doiTuongSelected ? "" : "Vui lòng chọn đối tượng"}>
  //         <Button type={"primary"} onClick={() => onPressXacNhan()} className="mr-2" iconPosition="end" icon={<CheckCircleOutlined />} disabled={doiTuongSelected ? false : true}>
  //           Chọn
  //         </Button>
  //       </Tooltip>
  //     </Form.Item>
  //   );
  // };
  return (
    <Flex vertical gap="middle" align="flex-start ">
      <Modal
        maskClosable={false}
        title="Đối tượng tài sản"
        // centered
        // bodyStyle={{maxHeight: "70vh", overflowY: "auto"}}

        className="modal-danh-sach-doi-tuong"
        open={IsOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        width={{
          xs: "75%",
          sm: "75%",
          md: "75%",
          lg: "75%",
          xl: "75%",
          xxl: "75%",
        }}
        style={{top: 30}}
        footer={null}>
        {renderHeaderTableDanhSachDoiTuong()}
        {renderTable()}
      </Modal>
      <ModalchiTietDoiTuong ref={refModalChiTietDoiTuong} />
    </Flex>
  );
});
//   const tableHeight = useTableHeight(["footer", "header"]);
ModalDanhSachDoiTuongComponent.displayName = "ModalDanhSachDoiTuongComponent";
export const ModalDanhSachDoiTuong = memo(ModalDanhSachDoiTuongComponent, isEqual);
