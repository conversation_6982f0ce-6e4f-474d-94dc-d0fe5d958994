import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  FileAddOutlined,
  PrinterFilled,
  ReloadOutlined,
  StopOutlined,
} from "@ant-design/icons";
// import {ReactQuery} from "@src/@types";
import {Button, HeaderModal, Popcomfirm} from "@src/components";
import {Flex, Form, message, Modal, Steps} from "antd";
import {Dayjs} from "dayjs";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useBaoHiemTaiSanContext} from "../index.context";
// import {initFormFields} from "../../CauHinhHopDong/BaoHiemXeCoGioi/Component/Constant";
// import {ThongTinCauHinhBaoHiemXeStep, ThongTinDoiTuongBaoHiemXeStep, ThongTinPheDuyetHopDongXeStep} from "../../CauHinhHopDong/BaoHiemXeCoGioi/Component/StepComponent";

import {initFormFields} from "./Constant";
import {ThongTinHopDongTaiSanStep} from "./StepComponent/ThongTinHopDongTaiSan_Step";
import {ReactQuery} from "@src/@types";
// import {IModalTrinhDuyetHopDongRef, ModalTrinhDuyetHopDongXe} from "./ModalTrinhDuyetHopDong";
import {formatDateTimeToNumber} from "@src/utils";
import {ThongTinDoiTuongBaoHiemTaiSanStep} from "./StepComponent/ThongTin_DoiTuongHopDongBaoHiemTaiSan_Step";
import ThongTin_HinhAnhHoSoTaiLieu_Step from "./StepComponent/ThongTin_HinhAnhHoSoTaiLieu_Step";
import {ThongTinCauHinhBaoHiemTaiSanStep} from "./StepComponent/ThongTin_CauHinhBaoHiemTaiSan_Step";
import {ThongTinPheDuyetHopDongTaiSanStep} from "./StepComponent/ThongTin_PheDuyetHopDongTaiSan_Step";
// import ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step from "./StepComponent/ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step";
// import {IModalDanhGiaTonThatRef} from "./StepComponent/Constant";

interface Props {
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
}

export interface IModalThemHopDongBaoHiemTaiSanRef {
  open: (data?: CommonExecute.Execute.IChiTietHopDongTaiSan) => void;
  close: () => void;
}

const ModalThemHopDongBaoHiemTaiSan = forwardRef<IModalThemHopDongBaoHiemTaiSanRef, Props>(({}: Props, ref) => {
  const {
    listDoiTac,
    listChiNhanh,
    listPhongBan,
    listPhuongThucKhaiThac,
    listSanPham,
    listDonViBoiThuong,
    listChuongTrinhBaoHiem,
    chiTietHopDongBaoHiemTaiSan,
    loading,
    chiTietDoiTuongBaoHiemTaiSan,
    updateDoiTuongBaoHiemTaiSan,
    getListDaiLyKhaiThac,
    huyHopDongTaiSan,
    goHuyHopDongTaiSan,
    updateHopDongBaoHiemTaiSan,
    layChiTietHopDongBaoHiemTaiSan,
    resetChiTietHopDongBaoHiemTaiSan,
    taoHopDongSuaDoiBoSung,
    danhSachDoiTuong
  } = useBaoHiemTaiSanContext();

  // Helper function để kiểm tra điều kiện disable khi không có chi tiết hợp đồng
  const isDisabledWhenNoContract = !chiTietHopDongBaoHiemTaiSan?.so_id || Object.keys(chiTietHopDongBaoHiemTaiSan).length === 0;

  const [formNhapHopDongTaiSan] = Form.useForm();
  const [formNhapDoiTuongBaoHiemTaiSan] = Form.useForm();
  const formNhapHopDongTaiSanValues = Form.useWatch([], formNhapHopDongTaiSan);
  const formNhapDoiTuongBaoHiemTaiSanValues = Form.useWatch([], formNhapDoiTuongBaoHiemTaiSan);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [listSanPhamFiltered, setListSanPhamFiltered] = useState<Array<CommonExecute.Execute.ISanPham>>([]);
  const [listChuongTrinhBaoHiemFiltered, setListChuongTrinhBaoHiemFiltered] = useState<Array<CommonExecute.Execute.IChuongTrinhBaoHiem>>([]);
  const [listPhuongThucKhaiThacFiltered, setListPhuongThucKhaiThacFiltered] = useState<Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>>([]);
  const [listDonViBoiThuongTPAFiltered, setListDonViBoiThuongTPAFiltered] = useState<Array<CommonExecute.Execute.INhaBaoHiemTPA>>([]);
  const [listChiNhanhFiltered, setListChiNhanhFiltered] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [listPhongBanFiltered, setListPhongBanFiltered] = useState<Array<CommonExecute.Execute.IPhongBan>>([]);
  const [daiLyKhaiThacSelected, setDaiLyKhaiThacSelected] = useState<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>(null);
  const [khachHangSelected, setKhachHangSelected] = useState<CommonExecute.Execute.IKhachHang | null>(null);
  const [canBoSelected, setCanBoSelected] = useState<CommonExecute.Execute.IDoiTacNhanVien | null>(null);
  //   let refModalTrinhDuyetHopDongXe = useRef<IModalTrinhDuyetHopDongRef>(null);
  const refModalDanhGiaTonThat = useRef<IModalDanhGiaTonThatRef>(null);

  // State cho download PDF
  const [downloadPDFFunction, setDownloadPDFFunction] = useState<(() => void) | null>(null);
  const [hasSelectedFile, setHasSelectedFile] = useState(false);

  // Callback để nhận download function từ child component
  const handleDownloadPDFCallback = useCallback((downloadFn: () => void, hasFile: boolean) => {
    setDownloadPDFFunction(() => downloadFn);
    setHasSelectedFile(hasFile);
  }, []);

  //watch
  const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formNhapHopDongTaiSan);

  useImperativeHandle(ref, () => ({
    open: (dataChiTietHopDongBaoHiemTaiSan?: CommonExecute.Execute.IChiTietHopDongTaiSan) => {
      setIsOpen(true);
      initData(dataChiTietHopDongBaoHiemTaiSan?.ma_doi_tac || "", dataChiTietHopDongBaoHiemTaiSan?.ma_chi_nhanh || "");
    },
    close: () => {
      // Reset form fields
      formNhapHopDongTaiSan.resetFields();
      formNhapDoiTuongBaoHiemTaiSan.resetFields();

      // Reset local state
      setCurrentStep(0);
      setIsOpen(false);
      setKhachHangSelected(null);
      setCanBoSelected(null);
      setDaiLyKhaiThacSelected(null);
      setDownloadPDFFunction(null);
      setHasSelectedFile(false);

      // Reset filtered lists
      setListSanPhamFiltered([]);
      setListChuongTrinhBaoHiemFiltered([]);
      setListPhuongThucKhaiThacFiltered([]);
      setListDonViBoiThuongTPAFiltered([]);
      setListChiNhanhFiltered([]);
      setListPhongBanFiltered([]);

      // Reset context data - quan trọng để tránh hiển thị dữ liệu cũ
      resetChiTietHopDongBaoHiemTaiSan();
    },
  }));

  useEffect(() => {
    if (watchDoiTacCapDon) {
      initListDaiLyKhaiThac();
    } else {
      // setListDaiLyKhaiThac([]);
    }
  }, [watchDoiTacCapDon]);

  useEffect(() => {
    if (chiTietHopDongBaoHiemTaiSan) {
      const arrFormData = [];
      for (const key in chiTietHopDongBaoHiemTaiSan) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietHopDongTaiSan,
          value: chiTietHopDongBaoHiemTaiSan[key as keyof CommonExecute.Execute.IChiTietHopDongTaiSan],
        });
      }
      formNhapHopDongTaiSan.setFields(arrFormData);
      //khai báo khách hàng được chọn
      const objectKhachHang: CommonExecute.Execute.IKhachHang = {
        ten: chiTietHopDongBaoHiemTaiSan?.ten_kh || "",
        ma: chiTietHopDongBaoHiemTaiSan?.ma_kh || "",
      };
      setKhachHangSelected(objectKhachHang);
      // khai báo cán bộ được chọn
      const objectCanBo: CommonExecute.Execute.IDoiTacNhanVien = {
        ten: chiTietHopDongBaoHiemTaiSan?.ten_cb_ql || "",
        ma: chiTietHopDongBaoHiemTaiSan?.ma_cb_ql || "",
      };
      setCanBoSelected(objectCanBo);
    }
  }, [chiTietHopDongBaoHiemTaiSan]);

  //khởi tạo các danh mục khi thay đổi đối tác
  const initData = async (maDoiTac: string, maChiNhanh: string) => {
    try {
      //init list phương thức khai thác filter theo đối tác
      if (listPhuongThucKhaiThac.length > 0 && maDoiTac !== "") {
        const filteredPhuongThucKhaiThacData = listPhuongThucKhaiThac.filter(item => item.ma_doi_tac_ql === maDoiTac);
        setListPhuongThucKhaiThacFiltered(filteredPhuongThucKhaiThacData);
      } else {
        setListPhuongThucKhaiThacFiltered([]);
      }
      //init list sản phẩm filter theo đối tác
      if (listSanPham.length > 0 && maDoiTac !== "") {
        const filteredSanPhamData = listSanPham.filter(item => item.ma_doi_tac_ql === maDoiTac && item.nv === "TS");
        setListSanPhamFiltered(filteredSanPhamData);
      } else {
        setListSanPhamFiltered([]);
      }
      //init list chi nhánh filter theo đối tác
      if (listChiNhanh.length > 0 && maDoiTac !== "") {
        const filteredChiNhanhData = listChiNhanh.filter(item => item.ma_doi_tac === maDoiTac);
        setListChiNhanhFiltered(filteredChiNhanhData);
      } else {
        setListChiNhanhFiltered([]);
      }
      //init list phòng ban filter theo đối tác và chi nhánh
      if (listPhongBan.length > 0 && maDoiTac !== "") {
        const filteredPhongBanData = listPhongBan.filter(item => item.ma_doi_tac === maDoiTac && item.ma_chi_nhanh === maChiNhanh);
        setListPhongBanFiltered(filteredPhongBanData);
      } else {
        setListPhongBanFiltered([]);
      }
      //init list đơn vị bảo hiểm TPA filter theo đối tác
      if (listDonViBoiThuong.length > 0 && maDoiTac !== "") {
        const filteredDonViBoiThuongData = listDonViBoiThuong.filter(item => item.ma_doi_tac_ql === maDoiTac);
        setListDonViBoiThuongTPAFiltered(filteredDonViBoiThuongData);
      } else {
        setListDonViBoiThuongTPAFiltered([]);
      }
      //init list chương trình bảo hiểm filter theo đối tác
      if (listChuongTrinhBaoHiem.length > 0 && maDoiTac !== "") {
        const filteredChuongTrinhBaoHiemData = listChuongTrinhBaoHiem.filter(item => item.ma_doi_tac_ql === maDoiTac && item.nv === "TS");
        setListChuongTrinhBaoHiemFiltered(filteredChuongTrinhBaoHiemData);
      } else {
        setListChuongTrinhBaoHiemFiltered([]);
      }
    } catch (error) {
      console.log("initData error", error);
    }
  };

  //đại lý
  const initListDaiLyKhaiThac = useCallback(async () => {
    try {
      const response = await getListDaiLyKhaiThac({ma_doi_tac_ql: watchDoiTacCapDon || "ESCS"});
      if (response.data) {
        const daiLyMatch = response.data.find(item => item.ma === chiTietHopDongBaoHiemTaiSan?.daily_kt);
        if (daiLyMatch) {
          setDaiLyKhaiThacSelected(daiLyMatch);
        }
      }
    } catch (error) {
      console.log("initListDaiLyKhaiThac error", error);
    }
  }, [watchDoiTacCapDon, getListDaiLyKhaiThac]);

  /** ĐỐI TÁC */
  useEffect(() => {
    if (chiTietDoiTuongBaoHiemTaiSan) {
      // const arrFormData = [];
      // for (const key in chiTietDoiTuongBaoHiemTaiSan) {
      //   arrFormData.push({
      //     name: key,
      //     value: chiTietDoiTuongBaoHiemTaiSan[key as keyof CommonExecute.Execute.IChiTietDoiTuongBaoHiemTaiSan],
      //   });
      // }
      const danhSachDoiTuongTTRR = danhSachDoiTuong.find(item => item.gcn?.dt_ttrr === chiTietDoiTuongBaoHiemTaiSan.gcn?.dt_ttrr);
      if (danhSachDoiTuongTTRR) {
        setDaiLySelected({
          ma: danhSachDoiTuongTTRR.gcn?.dt_ttrr,
          ten: danhSachDoiTuongTTRR.gcn.,
        });
      } else {
        setDaiLySelected(null);
      }
      formNhapDoiTuongBaoHiemTaiSan.setFields(arrFormData);
    }
  }, [chiTietDoiTuongBaoHiemTaiSan]);
  // init form data gọi vào index.configs

  useEffect(() => {
    initFormFields(formNhapHopDongTaiSan, chiTietHopDongBaoHiemTaiSan);
  }, [chiTietHopDongBaoHiemTaiSan]);

  //Bấm Update
  const onConfirm = async (action: string) => {
    const formatTime = (value: Dayjs | string | undefined) => {
      if (!value) return "";
      return typeof value === "string" ? value : value.format("HH:mm");
    };

    if (currentStep === 0) {
      //lưu thông tin hợp đồng
      try {
        await formNhapHopDongTaiSan.validateFields();
        const formNhapHopDongValues: ReactQuery.IUpdateHopDongParams = formNhapHopDongTaiSan.getFieldsValue();
        const params: ReactQuery.IUpdateHopDongParams = {
          ...formNhapHopDongValues,
          nv: "TS",
          ngay_cap: formNhapHopDongValues.ngay_cap ? formatDateTimeToNumber(formNhapHopDongValues.ngay_cap) : 0,
          ngay_hl: formNhapHopDongValues.ngay_hl ? formatDateTimeToNumber(formNhapHopDongValues.ngay_hl) : 0,
          ngay_kt: formNhapHopDongValues.ngay_kt ? formatDateTimeToNumber(formNhapHopDongValues.ngay_kt) : 0,
          gio_hl: formNhapHopDongValues.gio_hl ? formatTime(formNhapHopDongValues.gio_hl) : "",
          gio_kt: formNhapHopDongValues.gio_kt ? formatTime(formNhapHopDongValues.gio_kt) : "",
          so_hd_g: formNhapHopDongValues.so_hd_g ? formNhapHopDongValues.so_hd_g : "",
          so_id: chiTietHopDongBaoHiemTaiSan ? chiTietHopDongBaoHiemTaiSan?.so_id : 0,
        };
        console.log("params", params);
        // return;
        const response = await updateHopDongBaoHiemTaiSan(params);
        console.log("response", response);
        if (response.success) {
          if (response.isNewContract && response.contractInfo) {
            // Trường hợp tạo hợp đồng mới (so_id = 0 hoặc không có)
            console.log("✅ Tạo hợp đồng mới thành công:", response.contractInfo);

            // Tự động load chi tiết hợp đồng vừa tạo
            await layChiTietHopDongBaoHiemTaiSan({
              so_id: response.contractInfo.so_id,
              ma_doi_tac_ql: params.ma_doi_tac_ql || "",
            });

            // Message đã được hiển thị trong provider: "Tạo hợp đồng thành công!"
            // Hiển thị thêm thông tin số hợp đồng
            message.success(`Số hợp đồng: ${response.contractInfo.so_hd}`);
          } else {
            // Trường hợp cập nhật hợp đồng có sẵn (so_id > 0)
            console.log("✅ Cập nhật hợp đồng có sẵn thành công");
            // Message "Cập nhật thông tin hợp đồng thành công!" đã được hiển thị trong provider
            // Dữ liệu đã được reload trong provider thông qua layDanhSachHopDongBaoHiemTaiSanPhanTrang
          }

          // Đóng modal nếu action là "close"
          if (action === "close") {
            closeModal();
          }
        } else {
          console.error("❌ Lỗi khi lưu hợp đồng:", response);
        }
      } catch (error) {
        console.log("onConfirm error", error);
      }
    }
    //   //lưu thông tin đối tượng
    if (currentStep === 1) {
      try {
        await formNhapDoiTuongBaoHiemTaiSan.validateFields();
        const formNhapDoiTuongValues: ReactQuery.ICapNhatDoiTuongBaoHiemTaiSanParams = formNhapDoiTuongBaoHiemTaiSan.getFieldsValue();
        console.log("formNhapDoiTuongValues", formNhapDoiTuongValues);
        const {ttinh, ...formValuesWithoutGcnCt} = (formNhapDoiTuongValues as any) || {};
        const params: ReactQuery.ICapNhatDoiTuongBaoHiemTaiSanParams = {
          ...formValuesWithoutGcnCt,
          ttinh: ttinh?.map((item: any) => ({
            ma: item?.ma,
            gia_tri: item?.gia_tri,
          })),
          ngay_cap: formatDateTimeToNumber(formValuesWithoutGcnCt?.ngay_cap),
          ngay_hl: formatDateTimeToNumber(formValuesWithoutGcnCt?.ngay_hl),
          ngay_kt: formatDateTimeToNumber(formValuesWithoutGcnCt?.ngay_kt),
          gio_hl: formatTime(formValuesWithoutGcnCt?.gio_hl),
          gio_kt: formatTime(formValuesWithoutGcnCt?.gio_kt),
          so_id_dt: formValuesWithoutGcnCt?.so_id_dt ?? 0,
          so_id: chiTietHopDongBaoHiemTaiSan ? chiTietHopDongBaoHiemTaiSan?.so_id : 0,
        };
        console.log("params cập nhật đối tượng", params);
        // return;
        const response = await updateDoiTuongBaoHiemTaiSan(params); //cập nhật lại đơn vị chi nhánh
        if (response && action === "close") {
          closeModal();
          formNhapDoiTuongBaoHiemTaiSan.resetFields();
        }
      } catch (error) {
        console.log("onConfirm error", error);
      }
    }
  };

  const closeModal = useCallback(() => {
    // Reset form fields
    formNhapHopDongTaiSan.resetFields();
    formNhapDoiTuongBaoHiemTaiSan.resetFields();

    // Reset local state
    setIsOpen(false);
    setCurrentStep(0);
    setKhachHangSelected(null);
    setCanBoSelected(null);
    setDaiLyKhaiThacSelected(null);
    setDownloadPDFFunction(null);
    setHasSelectedFile(false);

    // Reset filtered lists
    setListSanPhamFiltered([]);
    setListChuongTrinhBaoHiemFiltered([]);
    setListPhuongThucKhaiThacFiltered([]);
    setListDonViBoiThuongTPAFiltered([]);
    setListChiNhanhFiltered([]);
    setListPhongBanFiltered([]);

    // Reset context data - quan trọng để tránh hiển thị dữ liệu cũ
    resetChiTietHopDongBaoHiemTaiSan();
  }, [formNhapHopDongTaiSan, formNhapDoiTuongBaoHiemTaiSan]);

  const onBack = () => {
    if (currentStep === 0) {
      closeModal();
      return;
    }
    if (currentStep === 1) formNhapDoiTuongBaoHiemTaiSan.resetFields();
    setCurrentStep(currentStep - 1);
  };

  const getMessAlertByCurrentStep = (current: number) => {
    switch (current) {
      case 0:
        return "Bạn có chắc chắn muốn lưu thông tin hợp đồng?";
      case 1:
        return "Bạn có chắc chắn muốn lưu thông tin đối tượng bảo hiểm?";
      case 2:
        return "Bạn có chắc chắn muốn lưu thông tin thanh toán?";
      case 3:
        return "Bạn có chắc chắn muốn lưu thông tin cấu hình?";
      case 4:
        return "Bạn có chắc chắn muốn lưu thông tin phê duyệt?";
      default:
        return "";
    }
  };

  const getTitleButtonBackByCurrentStep = (current: number) => {
    switch (current) {
      case 0:
        return "Đóng";
      default:
        return "Quay lại";
    }
  };

  const getIconButtonBackByCurrentStep = (current: number) => {
    switch (current) {
      case 0:
        return <CloseOutlined />;
      default:
        return <ArrowLeftOutlined />;
    }
  };

  useEffect(() => {
    if (currentStep === 0) {
      formNhapHopDongTaiSan
        .validateFields({validateOnly: true})
        .then(response => {
          setDisableSubmit(false);
        })
        .catch(error => {
          if (formNhapHopDongTaiSanValues.kieu_hd === "G" && error?.errorFields[0]?.name[0] === "so_hd_g") {
            setDisableSubmit(false);
          } else setDisableSubmit(true);
        });
    } else if (currentStep === 1) {
      console.log("formNhapDoiTuongBaoHiemTaiSanValues", formNhapDoiTuongBaoHiemTaiSan);
      formNhapDoiTuongBaoHiemTaiSan

        .validateFields({validateOnly: true})
        .then(response => {
          setDisableSubmit(false);
        })
        .catch(error => {
          setDisableSubmit(true);
        });
    } else {
      setDisableSubmit(false);
    }
  }, [currentStep, formNhapHopDongTaiSan, formNhapDoiTuongBaoHiemTaiSan, formNhapHopDongTaiSanValues, formNhapDoiTuongBaoHiemTaiSanValues]);

  // Thêm hàm xử lý khi click vào nút "Tạo SĐBS"
  const handleTaoHopDongSDBS = async () => {
    if (!chiTietHopDongBaoHiemTaiSan) return;

    try {
      const params: ReactQuery.ITaoHopDongSuaDoiBoSungParams = {
        ma_doi_tac_ql: chiTietHopDongBaoHiemTaiSan.ma_doi_tac_ql || "",
        so_id_g: chiTietHopDongBaoHiemTaiSan.so_id || 0,
      };

      const result = await taoHopDongSuaDoiBoSung(params);
      if (result) {
        // Nếu tạo thành công, có thể thực hiện thêm các hành động khác ở đây
        // Ví dụ: Đóng modal hiện tại và mở modal mới với hợp đồng SĐBS
        layChiTietHopDongBaoHiemTaiSan({
          so_id: result.so_id,
          ma_doi_tac_ql: chiTietHopDongBaoHiemTaiSan.ma_doi_tac_ql || "",
        });
      }
    } catch (error) {
      console.error("Lỗi khi tạo hợp đồng SĐBS:", error);
    }
  };

  //renderFooter Modal
  const renderFooter = () => {
    const showButtonHuy = Number(chiTietHopDongBaoHiemTaiSan?.ngay_huy_num) >= 30000101;
    const showButtonGoHuy = Number(chiTietHopDongBaoHiemTaiSan?.ngay_huy_num) < 30000101;
    const showButtonHuyTrinh = Number(chiTietHopDongBaoHiemTaiSan?.ngay_trinh_num) < 30000101;
    const disableButtonSDBS = chiTietHopDongBaoHiemTaiSan?.ngay_duyet_num && Number(chiTietHopDongBaoHiemTaiSan?.ngay_duyet_num) >= 30000101;
    return (
      <Form.Item className="mb-0" style={{width: "100%"}}>
        <div className="flex w-full items-center justify-between">
          <div>
            {currentStep === 0 && (
              <>
                <Popcomfirm
                  title="Thông báo"
                  onConfirm={handleTaoHopDongSDBS}
                  okText="Đồng ý"
                  description={`Bạn có chắc chắn muốn tạo sửa đổi bổ sung cho hợp đồng ${chiTietHopDongBaoHiemTaiSan?.so_hd || ""} này không?`}
                  buttonTitle="Tạo SĐBS"
                  buttonClassName="mr-2"
                  buttonIcon={<FileAddOutlined />}
                  type="primary"
                  buttonDisable={!!disableButtonSDBS || isDisabledWhenNoContract}
                />
                <Button type="primary" className="mr-2" icon={<PrinterFilled />}>
                  Xem hợp đồng
                </Button>

                {/* BUTTON HUỶ HỢP ĐỒNG XE */}
                {showButtonHuy && (
                  <Popcomfirm
                    danger
                    variant="outlined"
                    title="Thông báo"
                    onConfirm={huyHopDongTaiSan}
                    okText="Đồng ý"
                    description="Bạn có chắc chắn muốn huỷ hợp đồng này hay không?"
                    buttonTitle="Huỷ hợp đồng"
                    buttonClassName="mr-2"
                    buttonIcon={<StopOutlined />}
                    type="default"
                  />
                )}
                {/* BUTTON GỠ HUỶ HĐ */}
                {showButtonGoHuy && (
                  <Popcomfirm
                    title="Thông báo"
                    onConfirm={goHuyHopDongTaiSan}
                    okText="Đồng ý"
                    description="Bạn có chắc chắn muốn gỡ huỷ hợp đồng này hay không?"
                    buttonTitle="Gỡ huỷ hợp đồng"
                    buttonClassName="mr-2"
                    buttonIcon={<ReloadOutlined />}
                    type="default"
                  />
                )}
                {/* BUTTON COPY HĐ */}
                <Button type="primary" className="mr-2" icon={<CopyOutlined />}>
                  Copy hợp đồng
                </Button>
              </>
            )}

            {/* CÁC BUTTON CHƯA LÀM ACTION */}
            {currentStep === 1 && (
              <>
                <Button type="primary" className="mr-2" icon={<CopyOutlined />}>
                  Copy đối tượng
                </Button>
                <Button type="primary" className="mr-2" icon={<EyeOutlined />}>
                  Xem GCN
                </Button>
                <Button danger type="default" className="mr-2" icon={<CloseOutlined />}>
                  Xoá đối tượng
                </Button>
              </>
            )}

            {/* Button Download PDF - hiển thị ở bên trái khi step 4 */}
            {currentStep === 4 && downloadPDFFunction && hasSelectedFile && (
              <Button type="primary" onClick={downloadPDFFunction} className="mr-2" icon={<DownloadOutlined />}>
                Tải xuống PDF
              </Button>
            )}
          </div>
          {/* Bên phải: các nút còn lại */}
          <div>
            {currentStep === 2 && (
              <Button
                type="primary"
                onClick={() => refModalDanhGiaTonThat?.current?.open({so_id: chiTietHopDongBaoHiemTaiSan?.so_id, so_id_dt: chiTietDoiTuongBaoHiemTaiSan?.gcn?.so_id_dt || 0})}
                className="mr-2"
                icon={<FileAddOutlined />}
                loading={loading}
                disabled={disableSubmit || isDisabledWhenNoContract}
                title={isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để đánh giá tổn thất" : undefined}>
                Đánh giá tổn thất
              </Button>
            )}
            <Button type="default" onClick={() => onBack()} icon={getIconButtonBackByCurrentStep(currentStep)}>
              {getTitleButtonBackByCurrentStep(currentStep)}
            </Button>
            {/* Button Tiếp theo - chỉ hiển thị khi không phải step 4 */}
            {currentStep !== 4 && (
              <Button
                disabled={isDisabledWhenNoContract}
                type="primary"
                onClick={() => {
                  setCurrentStep(currentStep + 1);
                }}
                className="ml-2"
                iconPosition="end"
                icon={<ArrowRightOutlined />}
                title={isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để tiếp tục" : undefined}>
                Tiếp theo
              </Button>
            )}

            {/* Các button phê duyệt - chỉ hiển thị khi step 4 */}
            {currentStep === 4 && !showButtonHuyTrinh && (
              <Button
                disabled={isDisabledWhenNoContract}
                type="primary"
                // onClick={() => refModalTrinhDuyetHopDongXe?.current?.open()}
                className="ml-2"
                icon={<EditOutlined />}
                title={isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để trình duyệt" : undefined}>
                Trình duyệt
              </Button>
            )}

            {currentStep === 4 && showButtonHuyTrinh && (
              <Popcomfirm
                danger
                title="Thông báo"
                // onConfirm={() => {
                //   huyTrinhPheDuyetHopDong();
                // }}
                okText="Đồng ý"
                description="Bạn có chắc chắn muốn huỷ trình hợp đồng này hay không?"
                buttonTitle="Huỷ trình"
                buttonClassName="ml-2"
                buttonIcon={<CloseOutlined />}
                type="default"
              />
            )}
            {(currentStep === 0 || currentStep === 1) && (
              <>
                <Button
                  type="primary"
                  onClick={() => onConfirm("save")}
                  className="ml-2"
                  icon={<CheckOutlined />}
                  loading={loading}
                  disabled={disableSubmit || (currentStep > 0 && isDisabledWhenNoContract)}
                  title={currentStep > 0 && isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để lưu" : undefined}>
                  Lưu
                </Button>
                <Button
                  type="primary"
                  onClick={() => onConfirm("close")}
                  className="ml-2"
                  icon={<CheckOutlined />}
                  loading={loading}
                  disabled={disableSubmit || (currentStep > 0 && isDisabledWhenNoContract)}
                  title={currentStep > 0 && isDisabledWhenNoContract ? "Cần có chi tiết hợp đồng để lưu" : undefined}>
                  Lưu và đóng
                </Button>
              </>
            )}
          </div>
        </div>
      </Form.Item>
    );
  };
  //Render
  const onChangeStep = (value: number) => {
    // Disable step navigation khi không có chi tiết hợp đồng
    if (isDisabledWhenNoContract) return;
    setCurrentStep(value);
  };

  const StepComponent = () => {
    return (
      <Steps
        size="small"
        // status='error'
        current={currentStep}
        percent={60}
        onChange={isDisabledWhenNoContract ? undefined : onChangeStep}
        style={{
          cursor: isDisabledWhenNoContract ? "not-allowed" : "pointer",
          opacity: isDisabledWhenNoContract ? 0.6 : 1,
        }}
        items={[
          {
            title: "Thông tin hợp đồng",
            disabled: isDisabledWhenNoContract,
            // status: 'error'
            // description: 'Thông tin đơn vị quản lý, khách hàng...',
          },
          {
            title: "Thông tin đối tượng bảo hiểm",
            disabled: isDisabledWhenNoContract,
            // subTitle: 'Left 00:00:08',
            // description: 'Thông tin xe ô tô, xe máy',
          },
          {
            title: "Hình ảnh / Hồ sơ / Tài liệu",
            disabled: isDisabledWhenNoContract,
            // description: 'Thông tin đồng tái, email, ...',
          },
          {
            title: "Thông tin cấu hình",
            disabled: isDisabledWhenNoContract,
            // description: 'Thông tin đồng tái, email, ...',
          },
          {
            title: "Phê duyệt hợp đồng",
            disabled: isDisabledWhenNoContract,
            // description: 'Thông tin đồng tái, email, ...',
          },
        ]}
      />
    );
  };

  // render modal
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        centered
        title={
          <HeaderModal
            title={chiTietHopDongBaoHiemTaiSan && chiTietHopDongBaoHiemTaiSan?.so_hd ? `Chi tiết hợp đồng ${chiTietHopDongBaoHiemTaiSan?.so_hd}` : "Tạo mới hợp đồng"}
            trang_thai_ten={chiTietHopDongBaoHiemTaiSan?.trang_thai_ten}
            trang_thai={chiTietHopDongBaoHiemTaiSan?.trang_thai}
          />
        }
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => {
          // Sử dụng closeModal để đảm bảo reset đầy đủ
          closeModal();
        }}
        footer={renderFooter}
        // loading={loading}
        closable
        maskClosable={false}
        width="100vw"
        style={{
          top: 0,
          left: 0,
          padding: 0,
        }}
        styles={{
          body: {
            height: "80vh",
          },
        }}
        className="custom-full-modal modal-them-hop-dong-bao-hiem-tai-san">
        {StepComponent()}
        {currentStep === 0 && (
          <ThongTinHopDongTaiSanStep
            onChangeDoiTac={initData}
            listDoiTac={listDoiTac}
            listChiNhanh={listChiNhanhFiltered}
            listPhongBan={listPhongBanFiltered}
            listSanPham={listSanPhamFiltered}
            listChuongTrinhBaoHiem={listChuongTrinhBaoHiemFiltered}
            listPhuongThucKhaiThac={listPhuongThucKhaiThacFiltered}
            listDaiLyKhaiThac={[]}
            listDonViBoiThuongTPA={listDonViBoiThuongTPAFiltered}
            formNhapHopDongTaiSan={formNhapHopDongTaiSan}
            initListDaiLyKhaiTac={initListDaiLyKhaiThac}
            khachHangSelected={khachHangSelected || {ten: "", ma: ""}}
            setKhachHangSelected={setKhachHangSelected}
            canBoSeleceted={canBoSelected || {ten: "", ma: ""}}
            setCanBoSelected={setCanBoSelected}
            daiLyKhaiThacSelected={daiLyKhaiThacSelected || {ten: "", ma: ""}}
            setDaiLyKhaiThacSelected={setDaiLyKhaiThacSelected}
          />
        )}
        {currentStep === 1 && <ThongTinDoiTuongBaoHiemTaiSanStep formNhapDoiTuongBaoHiemTaiSan={formNhapDoiTuongBaoHiemTaiSan} />}
        {currentStep === 2 && <ThongTin_HinhAnhHoSoTaiLieu_Step />}
        {currentStep === 3 && <ThongTinCauHinhBaoHiemTaiSanStep pageSize={13} />}
        {currentStep === 4 && <ThongTinPheDuyetHopDongTaiSanStep onDownloadPDF={handleDownloadPDFCallback} />}
      </Modal>
      {/* <ModalTrinhDuyetHopDongXe ref={refModalTrinhDuyetHopDongXe} /> */}
      {/* <ModalDanhGiaTonThat ref={refModalDanhGiaTonThat} /> */}
    </Flex>
  );
});

ModalThemHopDongBaoHiemTaiSan.displayName = "ModalThemHopDongBaoHiemTaiSan";
export default ModalThemHopDongBaoHiemTaiSan;
