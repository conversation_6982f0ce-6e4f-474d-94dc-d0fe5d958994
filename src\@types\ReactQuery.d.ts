import {UseQueryOptions} from "react-query/types/react/types";

declare namespace ReactQuery {
  type Options = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, "queryKey" | "queryFn">;

  interface IPhanTrang {
    trang?: number;
    so_dong?: number;
  }

  //DANG_NHAP: "D2UHP2EWWRRXN68",
  interface ILoginParams {
    tai_khoan: string;
    mat_khau: string;
    actionCode?: string;
  }
  //Refresh Access Token khi hết hạn
  interface IRefreshAcessTokenParams {
    token: string;
    refresh_token: string;
  }

  //LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM: "TX4JC442LQ879WA",
  interface ILayDanhSachNguoiDungTheoNhomParams {
    nhom?: "CLIENT" | "ADMIN" | string;
  }

  // TIM_KIEM_DANH_SACH_DOI_TAC_PHAN_TRANG: "O8E0FCFMNEPY9RJ", //l<PERSON>y danh sách đối tác
  // D : <PERSON><PERSON> sử dụng
  // K : Ngừng sử dụng
  interface ILayDanhSachDoiTacParams {
    ma?: string;
    ten?: string;
    mst?: string;
    trang_thai?: string;
  }

  interface IChiTiethDoiTacParams {
    ma?: string;
  }

  interface IUpdateDoiTacParams {
    ma?: string;
    ten?: string;
    ten_tat?: string;
    ten_e?: string;
    mst?: string;
    dchi?: string;
    dthoai?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // CHI_TIET_PHONG_BAN: "WZF2ZP94BSUY0IA", //lấy chi tiết phòng ban
  interface IChiTietPhongBanParams {
    ma_doi_tac?: string;
    ma_chi_nhanh?: string;
    ma?: string;
    actionCode?: string;
  }

  // LUU_CAP_NHAT_PHONG_BAN: "QEJPX5IBKVB8MIO", // Lưu/Cập nhật thông tin chi tiết phòng ban
  interface IUpdatePhongBanParams {
    ma_doi_tac?: string;
    ma_chi_nhanh?: string;
    ma?: string;
    ten?: string;
    ten_tat?: string;
    dthoai?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // DANH_SACH_PHONG_BAN_PHAN_TRANG: "ITQ5QHJ70RT7DCN", // lấy dữ liệu danh sách phòng ban phân trang
  interface ILayDanhSachPhongBanPhanTrangParams {
    ma_doi_tac?: string;
    ma_chi_nhanh?: string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  // TIM_KIEM_DANH_SACH_DON_VI_CHI_NHANH_PHAN_TRANG: "2JZBKWFK6IHO3R8", //lấy danh sách đối tác
  // D : Đang sử dụng
  // K : Ngừng sử dụng
  interface ILayDanhSachDonViChiNhanhParams {
    ma_doi_tac?: string;
    ma?: string;
    ten?: string;
    mst?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
  }

  // GET_CHI_TIET_DON_VI_CHI_NHANH: "6SMTZNKDPHMTXSZ", //lấy danh sách đối tác
  interface IChiTietDonViChiNhanhParams {
    ma?: string;
    ma_doi_tac?: string;
  }

  interface IUpdateDonViChiNhanhParams {
    ma?: string;
    ten?: string;
    ten_tat?: string;
    email?: string;
    mst?: string;
    dchi?: string;
    dthoai?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  //  GET_DANH_SACH_TAI_KHOAN_NGUOI_DUNG: "QFMKIU48TG39846", //LẤY DANH SÁCH TÀI KHOẢN NGƯỜI DÙNG
  interface ILayDanhSachTaiKhoanNguoiDungPhanTrangParams {
    ma_doi_tac?: string;
    ma_chi_nhanh?: string;
    nd_tim?: string;
    trang_thai?: string;
    phong?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  // GET_CHI_TIET_TAI_KHOAN_NGUOI_DUNG: "RRYE6JO47VEWF01", //lấy chi tiết tài khoản ng dùng
  interface IChiTietTaiKhoanNguoiDungParams {
    ma_doi_tac?: string;
    ma?: string;
    actionCode?: string;
  }

  // LIET_KE_DANH_SACH_PHONG_BAN: "XGUE3LYQ1G9RBA2", //Lấy danh mục phòng ban dùng chung
  interface ILayDanhMucPhongBanParams {
    actionCode?: string;
  }
  // GET_DANH_SACH_CHUC_NANG_PHAN_TRANG: "KXC0SGHLGB67Q1A", //LẤY DANH SÁCH HỆ THỐNG CHỨC NĂNG PHÂN TRANG
  interface ILayDanhSachChucNangPhanTrangParams {
    ten?: string;
    loai?: string;
    kieu_ad?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  // GET_CHI_TIET_CHUC_NANG: "RKN4HSMWJ866KPI", //LẤY CHI TIẾT HỆ THỐNG CHỨC NĂNG
  interface IChiTietChucNangParams {
    ma?: string;
    actionCode?: string;
  }
  // LUU_CAP_NHAT_CHUC_NANG: "S65LB7TVUWDPQBE", // Lưu/Cập nhật thông tin chi tiết chức năng
  interface IUpdateChucNangParams {
    ma?: string;
    ten?: string;
    loai?: string;
    kieu_ad?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // GET_DANH_SACH_NHOM_CHUC_NANG_PHAN_TRANG: "NZHBBMMAJA2W6VW", //LẤY DANH SÁCH NHÓM CHỨC NĂNG PHÂN TRANG
  interface ILayDanhSachNhomChucNangPhanTrangParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietNhomChucNangParams {
    ma?: string;
    actionCode?: string;
  }

  interface ILayDanhSachChucDanhPhanTrangParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
  }

  interface IChiTietChucDanhParams {
    ma?: string;
    ma_doi_tac_ql?: string;
  }

  interface IUpdateChucDanhParams {
    ma?: string;
    ma_doi_tac_ql?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  interface ILayDanhSachKhachHangPhanTrangParams {
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    loai_kh?: string;
    ma?: string;
    ten?: string;
    dthoai?: string;
    cmt?: string;
    mst?: string;
    nd_tim?: string;
    trang?: number;
    so_dong?: number;
  }

  // API lấy chi tiết khách hàng - Action code: 2E5TKFPUNJ63CMJ
  interface ILayChiTietKhachHangParams {
    ma_doi_tac_ql: string; // Mã đối tác quản lý (required)
    ma_chi_nhanh_ql?: string; // Mã chi nhánh quản lý (optional)
    ma: string; // Mã khách hàng (required)
  }

  // API lưu thông tin khách hàng - Action code: G0RX0VG9U7T7ZXH
  interface ILuuThongTinKhachHangParams {
    ma_doi_tac_ql: string; // Mã đối tác quản lý - Select dropdown (required)
    ma_chi_nhanh_ql?: string; // Mã chi nhánh quản lý - Select dropdown
    loai_kh?: string; // Loại khách hàng - Select dropdown
    ma?: string; // Mã khách hàng - Text input
    ten?: string; // Tên khách hàng - Text input
    dchi?: string; // Địa chỉ - Text input
    mst?: string; // Mã số thuế - Text input
    cmt?: string; // Chứng minh thư - Text input
    dthoai?: string; // Điện thoại - Text input
    email?: string; // Email - Text input
    nguoi_lhe?: string; // Người liên hệ - Text input
    dthoai_lhe?: string; // Điện thoại liên hệ - Text input
    email_lhe?: string; // Email liên hệ - Text input
    trang_thai?: string; // Trạng thái - Select dropdown
  }

  interface ILayDanhSachHopDongConNguoiPhanTrangParams {
    tu_ngay?: number;
    den_ngay?: number;
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    nv?: string;
    trang_thai?: string;
    nd_trim?: string;
    so_hd?: string | number;
    gcn?:
      | string
      | Array<{
          so_id_dt?: number | string;
          tl_dong?: number;
        }>;
    ten_ndbh?: string;
    cmt_ndbh?: string;
    dthoai_ndbh?: string;
    trang?: number;
    so_dong?: number;
  }
  //GET_DS_HD_BH_XCG_PHAN_TRANG: "UWZX9683M0906FK", //LẤY DANH SÁCH HỢP ĐỒNG BH XE PHÂN TRANG
  interface ILayDanhSachHopDongXePhanTrangParams {
    ngay_d?: string;
    ngay_c?: string;
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    nd?: string; // ten khach hang, so hop dong,số GCN
    bien_xe?: string; //Biển xe , số khung , số máy
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
    nv: string;
  }
  interface ITimKiemPhanTrangDanhSachDaiLyParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    loai?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IUpdateDaiLyParams {
    // ma_ct?: string;
    ma?: string;
    ma_doi_tac_ql?: string;
    ten?: string;
    nguoi_dd?: string;
    dthoai_dd?: string;
    email_dd?: string;
    mst_dd?: string;
    cmt_dd?: string;
    stt?: number;
    loai?: string;
    trang_thai?: string;
    actionCode?: string;
  }

  interface IUpdateHopDongParams {
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    nv?: string;
    phong_ql?: string;
    ma_cb_ql?: string;
    so_id?: number | string;
    so_hd?: number | string;
    kieu_hd?: string;
    so_hd_g?: string;
    ma_sp?: string;
    ma_ctbh?: string;
    ngay_cap?: string | number;
    gio_hl?: string;
    ngay_hl?: string | number;
    gio_kt?: string;
    ngay_kt?: string | number;
    ma_kh?: string;
    daily_kt?: string;
    pt_kt?: string;
    vip?: string;
    ma_nha_tpa?: string; //đơn vị bồi thường
  }
  interface ITimKiemPhanTrangPhuongThucKhaiThacParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface ILietKePhuongThucKhaiThacParams {
    ma_doi_tac_ql?: string;
    actionCode?: string;
  }
  interface IChiTietDanhMucDaiLyParams {
    ma?: string;
    ma_doi_tac_ql?: string;
  }

  interface IlayChiTietPhuongThucKhaiThacParams {
    ma_doi_tac_ql?: string;
    ma?: string;
  }

  interface ITimKiemPhanTrangCanBoQuanLyParams {
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    phong_ql?: string;
    ma?: string;
    ten?: string;
    cmt?: string;
    dthoai?: string;
    email?: string;
    nd_tim?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface ILietKeSanPhamParams {
    ma_doi_tac_ql?: string;
    nv?: string; //NG - TS - KT - TN - HH - XE - XE_MAY
    actionCode?: string;
  }

  interface ILietKeChuongTrinhBaoHiemParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    nv?: string;
    nhom?: string;
    actionCode?: string;
  }

  interface ILietKeDanhSachDaiLyParams {
    ma_doi_tac_ql?: string;
  }

  interface ILietKeDonViBoiThuongTPAParams {
    nv?: string;
    nhom?: string;
  }
  interface IUpdatePhuongThucKhaiThacParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  interface ITimKiemPhanTrangDanhSachSanPhamParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    nv?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    // actionCode?: string;
  }
  interface IChiTietDanhMucSanPhamParams {
    ma?: string;
    nv?: string;
    ma_doi_tac_ql?: string;
  }
  interface IUpdateDanhMucSanPhamParams {
    ma?: string;
    nv?: string;
    ma_doi_tac_ql?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  interface ITimKiemPhanTrangBoMaQuyenLoiParams {
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    nd_tim?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietBoMaQuyenLoiParams {
    ma_doi_tac_ql?: string;
    ma_sp?: string;
    ma?: string;
    nv?: string;
  }
  interface IUpdateBoMaQuyenLoiParams {
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    ma?: string;
    ten?: string;
    ten_e?: string;
    loai?: string;
    qloi_gh?: string;
    ma_ct?: string;
    bl_nt?: string;
    bl_gt?: string;
    bl_ra?: string;
    ma_dtac?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  interface IChiTietHopDongXeParams {
    so_id?: number | string;
    ma_doi_tac_ql?: string;
  }

  interface IChiTietHopDongConNguoiParams {
    so_id?: number | string;
    ma_doi_tac_ql?: string;
  }

  interface ITimKiemPhanTrangBoMaNguyenTeParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IlayChiTietBoMaNguyenTeParams {
    ma_doi_tac_ql?: string;
    ma?: string;
  }
  interface IUpdateBoMaNguyenTeParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  interface ITimKiemPhanTrangDoiTuongBaoHiemXeParams {
    so_id?: number | string;
    // gcn?: string;
    // ten?: string;
    nd_tim?: string;
    trang?: number;
    so_dong?: number;
    ma_dvi_dong_tai?: string;
    dong_tai?: string;
  }
  interface ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi {
    so_id?: number | string;
    so_hd?: string | number;
    gcn?:
      | string
      | Array<{
          so_id_dt?: number | string;
          tl_dong?: number;
        }>;
    ten?: string;
    ngay_sinh?: string | number;
    so_cmt?: string;
    dthoai?: string;
    nd_tim?: string;
    trang?: number;
    so_dong?: number;
  }

  interface IChiTietNguoiDuocBaoHiemHopDongConNguoi {
    so_id?: string | number;
    so_id_dt?: number | string;
  }

  interface ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi {
    so_id?: string | number;
    so_id_dt?: number | string;
    gcn?:
      | string
      | Array<{
          so_id_dt?: number | string;
          tl_dong?: number;
        }>;
    ten?: string;
    dia_chi?: string;
    ngay_sinh?: string | number;
    gioi_tinh?: string;
    so_cmt?: string;
    dthoai?: string;
    email?: string;
    ma_goi_bh?: string;
    vip?: string;
    ngay_cap?: string | number;
    gio_hl?: string;
    ngay_hl?: string | number;
    gio_kt?: string;
    ngay_kt?: string | number;
    cty_ctac?: number;
    cnhanh_ctac?: string;
    pban_ctac?: string;
    ma_nv_ctac?: string;
    cvu_ctac?: string;
    email_ctac?: string;
  }

  interface ITimkiemPhanTrangHeThongMenuParams {
    ma?: string;
    ten?: string;
    nhom?: string;
    trang_thai?: string;
    app_name?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietHeThongMenuParams {
    ma?: string;
  }
  interface ICapNhatHeThongMenuParams {
    ma?: string;
    ten?: string;
    nhom?: string;
    ma_cha?: string;
    url?: string;
    icon?: string;
    stt?: number;
    app_name?: string;
    trang_thai?: string;
    actionCode?: string;
  }
  interface IFormCapNhatDoiTuongBaoHiemXeCoGioiParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    ten?: string;
    dchi?: string;
    gcn?:
      | string
      | Array<{
          so_id_dt?: number | string;
          tl_dong?: number;
        }>;
    bien_xe?: string;
    so_khung?: string;
    so_may?: string;
    loai_xe?: string;
    hang_xe?: string;
    hieu_xe?: string;
    nam_sx?: string;
    md_sd?: string;
    so_cho?: string;
    so_nguoi_bh?: string;
    trong_tai?: string;
    so_lphu_xe?: string;
    gia_tri?: number;
    gio_hl?: string;
    ngay_hl?: string | number;
    gio_kt?: string;
    ngay_kt?: string | number;
    ngay_cap?: string | number;
    vip?: string;
    dk?: Array<string>;
    dkbs?: Array<{
      ma_qloi?: string;
      ten_qloi?: string;
      ma_qloi_ct?: string;
      gh_lan_ngay?: number;
      gh_tien_lan_ngay?: number;
      gh_lan_ngay_dtri?: number;
      gh_tien_lan_ngay_dtri?: number;
      gh_tien_nam?: number;
      tgian_cho?: number;
      nt_tien_bh?: string;
      ma_qloi_tru_lui?: string;
      kieu_ad?: string;
      tl_dct?: number;
      phi_bh?: number;
      ghi_chu?: string;
    }>;
  }
  interface IChiTietDoiTuongBaoHiemXeParams {
    so_id?: number | string;
    so_id_dt?: number | string;
  }
  interface ITimKiemPhanTrangDanhMucNganHangParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietDanhMucNganHangParams {
    ma?: string;
  }
  interface ICapNhatDanhMucNganHangParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    stt?: number;
    actionCode?: string;
  }
  interface ITimKiemPhanTrangDanhMucHangXeParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    ngay_tao?: string | number;
    nguoi_tao?: string;
    ngay_cap_nhat?: string | number;
    nguoi_cap_nhat?: string;
    actionCode?: string;
    nv?: string;
  }
  interface ILayChiTietDanhMucHangXeParams {
    ma?: string;
  }
  interface IUpdateDanhMuchangXeParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
    nv?: string;
  }

  interface ITimKiemPhanTrangDanhMucHieuXeParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    ngay_tao?: string | number;
    nguoi_tao?: string;
    ngay_cap_nhat?: string | number;
    nguoi_cap_nhat?: string;
    actionCode?: string;
    nv?: string;
    hang_xe?: string;
  }
  interface ILayChiTietDanhMucHieuXeParams {
    ma?: string;
  }
  interface IUpdateDanhMucHieuXeParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
    nv?: string;
    hang_xe?: string;
  }
  interface ITimKiemPhanTrangChiNhanhNganHangParams {
    ten?: string;
    ma_ngan_hang?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietChiNhanhNganHangParams {
    ma_ngan_hang?: string;
    ma?: string;
  }

  // Params cho cập nhật
  interface ICapNhatChiNhanhNganHangParams {
    ma_ngan_hang?: string;
    ma?: string; // Không có khi thêm mới
    ten?: string;
    trang_thai?: string;
    stt?: number;
  }

  interface ITimKiemPhanTrangChuongTrinhBaoHiemParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    nv?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietGoiBaoHiem {
    id?: string;
  }
  interface ICapNhatGoiBaoHiem {}
  interface IChiTietChuongTrinhBaoHiemParams {
    ma?: string;
    ma_doi_tac_ql?: string;
    nv?: string;
  }
  interface ICapNhatChuongTrinhBaoHiemParams {
    ma_doi_tac_ql?: string;
    ma?: string;
    ten?: string;
    nv?: string;
    ma_sp?: string;
    ngay_ad?: string | number;
    mo_ta?: string;
    trang_thai?: string;
    stt?: number;
    actionCode?: string;
  }
  interface ILietKeGoiBaoHiemParams {
    ma_doi_tac_ql?: string;
    ma_ctbh?: string;
    nv?: string;
    actionCode?: string;
  }
  //GÓI BẢO HIỂM CON NGƯỜI
  interface ITimKiemPhanTrangGoiBaoHiemParams {
    ma_doi_tac_ql?: string;
    ma_sp?: string; // 1 gói sẽ phải thuộc 1 sản phẩm nào đó
    ma?: string;
    ten?: string;
    nd_tim?: string;
    ngay_ad?: number | string; //NGÀY ÁP DỤNG
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietGoiBaoHiemParams {
    id?: number;
    actionCode?: string;
  }
  interface ICapNhatGoiBaoHiemParams {
    ma_doi_tac_ql?: string;
    id?: number;
    nv?: string;
    ma_sp?: string; //MÃ SẢN PHẨM
    ma?: string;
    ten?: string;
    ngay_ad?: number; //NGÀY ÁP DỤNG
    gioi_tinh?: string; //GIỚI TÍNH
    tuoi_tu?: number; //TUỔI TỪ
    tuoi_toi?: number; //TUỔI TỚI
    ma_ct?: string; // MÃ CẤP TRÊN
    trang_thai?: string;

    //QUYỀN LỢI BẢO HIỂM
    goi_bh?: Array<{
      ma_qloi?: string;
      ten_qloi?: string;
      ma_qloi_ct?: string;
      gh_lan_ngay?: number;
      gh_tien_lan_ngay?: number;
      gh_lan_ngay_dtri?: number;
      gh_tien_lan_ngay_dtri?: number;
      gh_tien_nam?: number;
      tgian_cho?: number;
      nt_tien_bh?: string;
      ma_qloi_tru_lui?: string;
      kieu_ad?: string;
      tl_dct?: number;
      phi_bh?: number;
      ghi_chu?: string;
    }>;

    //ĐIỀU KHOẢN BỔ SUNG
    dkbs?: Array<{
      ma_qloi?: string;
      ten_qloi?: string;
      ma_qloi_ct?: string;
      gh_lan_ngay?: number;
      gh_tien_lan_ngay?: number;
      gh_lan_ngay_dtri?: number;
      gh_tien_lan_ngay_dtri?: number;
      gh_tien_nam?: number;
      tgian_cho?: number;
      nt_tien_bh?: string;
      ma_qloi_tru_lui?: string;
      kieu_ad?: string;
      tl_dct?: number;
      phi_bh?: number;
      ghi_chu?: string;
    }>;

    actionCode?: string;
  }
  //Xóa gói bảo hiểm ra khỏi CTBH
  interface IXoaGoiBaoHiemKhoiCTBHParams {
    ma_doi_tac_ql?: string;
    ma_ctbh?: string;
    id_goi_bh?: number;
    ma_sp?: string;
    nv?: string;
  }
  interface ICapNhatGoiCTBHParams {
    ma_doi_tac_ql?: string;
    ma_ctbh?: string;
    ma_sp?: string;
    nv?: string;
    goi_bh: Array<{
      id?: number;
      chon?: string;
    }>;
  }

  // ===== DANH MỤC LOẠI XE =====

  // Params tìm kiếm phân trang danh mục loại xe
  interface ITimKiemPhanTrangDanhMucLoaiXeParams {
    nv?: string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  // Params chi tiết loại xe
  interface IChiTietDanhMucLoaiXeParams {
    ma?: string;
    nv?: string;
  }

  // Params cập nhật (thêm mới/chỉnh sửa) loại xe
  interface ICapNhatDanhMucLoaiXeParams {
    ma?: string;
    ten?: string;
    nv?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  interface ILayThongTinThanhToanCuaHopDongBaoHiemParams {
    so_id?: number | string;
  }
  interface IUpdateKyThanhToanParams {
    so_id?: number | string;
    ky_tt?: number;
    so_tien?: number;
    so_id_ky_ttoan?: number;
    ttoan?: Array<{
      bt: number;
      so_tien_da_tt: number;
      ngay_tt: number;
      so_ct: string;
    }>;
  }
  interface IChiTietKyThanhToanParams {
    so_id_d?: number;
    so_id_ky_ttoan?: number;
  }
  interface ILayThongTinDongTaiBHCuaHopDongBaoHiemParams {
    so_id?: number | string;
  }
  interface IUpdateCauHinhDongBHParams {
    so_id?: number | string;
    ma_dvi_dong?: string;
    loai_dong?: string;
    kieu_dong?: string;
    tl_dong?: string;
  }

  // IChiTietThongTinCauHinhDongBHParams Dùng chung cho xem chi tiết và xoá thông tin đồng BH
  interface IChiTietThongTinCauHinhDongBHParams {
    so_id?: number | string;
    ma_dvi_dong?: string;
  }

  // Mức độ tổn thất xe
  interface ITimKiemPhanTrangMucDoTonThatXeParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietMucDoTonThatXeParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatMucDoTonThatXeParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // ========== DANH MUC BENH VIEN ==========
  interface ITimKiemPhanTrangDanhMucBenhVienParams {
    nd_tim?: string;
    nhom_bv?: string;
    tinh_thanh?: string;
    // tk_ngan_hang?: string;
    // tk_chi_nhanh?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  // CHI_TIET_DANH_MUC
  interface IChiTietDanhMucBenhVienParams {
    ma?: number | string;
    actionCode?: string;
  }

  // UPDATE
  interface ICapNhatDanhMucBenhVienParams {
    ma?: number | string;
    ten?: string;
    mst?: string;
    dia_chi?: string;
    dthoai?: string;
    email?: string;
    nhom_bv?: string;
    loai?: string;
    tinh_thanh?: string;
    ad_bhyt?: string;
    bl_nt?: string;
    bl_gt?: string;
    bl_ra?: string;
    tk_ngan_hang?: string;
    tk_chi_nhanh?: string;
    tk_so?: string;
    tk_ten?: string;
    nguoi_lhe?: string;
    dthoai_lhe?: string;
    email_lhe?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  // ========== DANH MUC BANG MA BENH ==========
  interface ITimKiemPhanTrangDanhMucBangMaBenhParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucBangMaBenhParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucBangMaBenhParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  // ========== DANH MUC NHOM MA BENH ==========
  interface ITimKiemPhanTrangDanhMucNhomMaBenhParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucNhomMaBenhParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucNhomMaBenhParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // ========== DANH MUC NHOM MA BENH ==========
  interface ITimKiemPhanTrangDanhMucNhomMaBenhParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucNhomMaBenhParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucNhomMaBenhParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  // ========== DANH MUC MA BENH ICD==========
  interface ITimKiemPhanTrangDanhMucMaBenhICDParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucMaBenhICDParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucMaBenhICDParams {
    ma?: string;
    ten?: string;
    ten_e?: string;
    ma_ct?: string;
    ma_byt?: string;
    // stt?: number;
    // trang_thai?: string;
    actionCode?: string;
  }

  // ========== DANH MUC CHAU LUC ==========
  interface ITimKiemPhanTrangDanhMucChauLucParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucChauLucParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucChauLucParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  //Khu vực
  interface ITimKiemPhanTrangDanhMucKhuVucParams {
    ma?: string;
    ma_chau_luc?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucKhuVucParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucKhuVucParams {
    ma?: string;
    ma_chau_luc?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  //Quốc gia
  interface ITimKiemPhanTrangDanhMucQuocGiaParams {
    ma?: string;
    ma_chau_luc?: string;
    ma_khu_vuc?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietDanhMucQuocGiaParams {
    ma?: string;
    actionCode?: string;
  }

  interface ICapNhatDanhMucQuocGiaParams {
    ma?: string;
    ma_chau_luc?: string;
    ma_khu_vuc?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // ========== DANH MUC TINH THANH ==========
  // TIM_KIEM_PHAN_TRANG_DANH_MUC_TINH_THANH: Tìm kiếm phân trang danh mục tỉnh thành
  interface ITimKiemPhanTrangDanhMucTinhThanhParams {
    ngay_ad?: number;
    ma?: string;
    ten?: string;
    // mien?: string; // TODO: BE
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  // CHI_TIET_DANH_MUC_TINH_THANH: Lấy chi tiết danh mục tỉnh thành
  interface IChiTietDanhMucTinhThanhParams {
    ngay_ad?: number;
    ma?: string;
    actionCode?: string;
  }

  // UPDATE_DANH_MUC_TINH_THANH: Cập nhật/thêm mới danh mục tỉnh thành
  interface ICapNhatDanhMucTinhThanhParams {
    ngay_ad?: number;
    ma?: string;
    ten?: string;
    stt?: number;
    mien?: string;
    trang_thai?: string;
    actionCode?: string;
  }

  //DANH MỤC QUẬN HUYỆN
  interface ITimKiemPhanTrangDanhMucQuanHuyenParams {
    ma_tinh?: string;
    ngay_ad?: string | string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietDanhMucQuanHuyenParams {
    ma_tinh?: string;
    ngay_ad?: number;
    ma?: string;
    actionCode?: string;
  }
  interface ICapNhatDanhMucQuanHuyenParams {
    ma_tinh?: string;
    ngay_ad?: number;
    ma?: string;
    ten?: string;
    stt?: number;
    postcode?: string;
    trang_thai?: string;
    actionCode?: string;
  }
  // DANH MỤC PHƯỜNG XÃ
  interface ITimKiemPhanTrangDanhMucPhuongXaParams {
    ma_tinh?: string;
    ma_quan?: string;
    ngay_ad?: string;
    ma?: string;
    ten?: string;
    trang_thai?: string;
  }
  interface IChiTietDanhMucPhuongXaParams {
    ma_tinh?: string;
    ma_quan?: string;
    ngay_ad?: number;
    ma?: string;
  }
  interface ICapNhatDanhMucPhuongXaParams {
    ma_tinh?: string;
    ma_quan?: string;
    ngay_ad?: number;
    ma?: string;
    ten?: string;
    stt?: number;
    postcode?: string;
    trang_thai?: string;
    actionCode?: string;
  }

  // ========== DANH MUC MÃ XE HẠNG MỤC NHÓM ==========
  interface ITimKiemPhanTrangNhomHangMucXeParams {
    ma?: string;
    nv?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietNhomHangMucXeParams {
    ma?: string;
    nv?: string;
    actionCode?: string;
  }

  interface ICapNhatNhomHangMucXeParams {
    ma?: string;
    nv?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  // ========== DANH MUC MÃ XE HẠNG MỤC ==========
  interface ITimKiemPhanTrangHangMucXeParams {
    ma?: string;
    nv?: string;
    ten?: string;
    loai?: string;
    nhom?: string;
    vi_tri?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  // ========== TÌM KIẾM PHÂN TRANG HẠNG MỤC TỔN THẤT ==========
  interface ITimKiemPhanTrangHangMucTonThatParams {
    nv?: string;
    ma?: string;
    ten?: string;
    loai?: string;
    nhom?: string;
    vi_tri?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietHangMucXeParams {
    ma?: string;
    nv?: string;
    actionCode?: string;
  }

  interface ICapNhatHangMucXeParams {
    ma?: string;
    nv?: string;
    ten?: string;
    loai?: string;
    nhom?: string;
    vi_tri?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }
  interface ITaoHopDongSuaDoiBoSungParams {
    ma_doi_tac_ql: string;
    so_id_g: number;
  }

  // Cấu hình mẫu hợp đồng

  interface ITimKiemPhanTrangCauHinhMauHopDongParams {
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    ten?: string;
    trang_thai?: string;
    ngay_ad?: number;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietCauHinhMauHopDongParams {
    bt?: number | string;
    actionCode?: string;
  }

  interface ICapNhatCauHinhMauHopDongParams {
    bt?: number | string;
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    ten?: string;
    ngay_ad?: number;
    trang_thai?: string;
    id_file?: number;
    actionCode?: string;
  }

  // Cấu hình mẫu giấy chứng nhận

  interface ITimKiemPhanTrangCauHinhMauGCNParams {
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    ten?: string;
    trang_thai?: string;
    ngay_ad?: number;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }

  interface IChiTietCauHinhMauGCNParams {
    bt?: number | string;
    actionCode?: string;
  }

  interface ICapNhatCauHinhMauGCNParams {
    bt?: number | string;
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    ten?: string;
    ngay_ad?: number;
    trang_thai?: string;
    id_file?: number;
    actionCode?: string;
  }

  //
  interface IUpdateCauHinhTaiBHParams {
    so_id?: number | string;
    ma_dvi_tai?: string;
    kieu_tai?: string;
    tl_tai?: string;
  }
  // IChiTietThongTinCauHinhDongBHParams Dùng chung cho xem chi tiết và xoá thông tin tái BH
  interface IChiTietThongTinCauHinhTaiBHParams {
    so_id?: number | string;
    ma_dvi_tai?: string;
  }
  interface IUpdateDoiTuongApDungDongBHParams {
    so_id?: number | string;
    ma_dvi_dong?: string;
    gcn?:
      | Array<{
          so_id_dt?: number | string;
          tl_dong?: number;
        }>
      | string;
  }
  interface IUpdateDoiTuongApDungTaiBHParams {
    so_id?: number | string;
    ma_dvi_tai?: string;
    gcn?:
      | Array<{
          so_id_dt?: number | string;
          tl_tai?: number;
        }>
      | string;
  }
  interface IUpdateTaiKhoanNguoiDungParams {
    ma_doi_tac?: string;
    ma_chi_nhanh?: string;
    phong?: string;
    ma?: string;
    ten?: string;
    mat_khau?: string;
    ma_chuc_danh?: string;
    dthoai?: string;
    emal?: string;
    ngay_hl?: string | number;
    ngay_kt?: string | number;
    trang_thai?: string;
    qly?: Array<{
      ma_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
    }>;
    menu?: Array<{
      ma?: string;
    }>;
    quyen?: Array<{
      ma?: string;
    }>;
  }
  interface ITimKiemPhanTrangVaiTroChucNangParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
  }
  interface ITimKiemPhanTrangNhomChucNangParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
  }
  interface IDanhSachChucNangTheoVaiTroParams {
    ma?: string;
    ma_vai_tro?: string;
  }
  interface ILietKeDanhSachNguoiDuyetParams {
    so_id?: number | string;
    nd_tim?: string;
    nhom_duyet?: string;
  }
  interface ITrinhPheDuyetHopDongParams {
    so_id?: number | string;
    nv?: string;
    nsd_duyet?: string;
    nd_trinh?: string;
  }
  interface IHuyTrinhPheDuyetHopDongParams {
    so_id?: number | string;
    nv?: string;
  }

  //interface này có thể dùng chung cho con người
  interface ITimKiemPhanTrangHopDongTrinhDuyetParams {
    nv?: string;
    tu_ngay?: number;
    den_ngay?: number;
    so_hd?: string | number;
    ten_kh?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
  }
  interface IDanhSachChucNangTheoNhomParams {
    ma?: string;
    ma_nhom?: string;
  }
  interface IXemChiTietHopDongTrinhDuyetParams {
    bt?: number | string;
  }
  interface ICapNhatHopDongTrinhDuyetParams {
    bt?: number | string;
    nd?: string;
  }

  //TẢI FILE LÊN
  interface IUploadFileParams {
    ma_doi_tac_ql?: string;
    bt?: number | string;
    ten?: string;
    quy_trinh?: string;
    nhom?: string;
    stt?: number;
    file_public?: number;
    id_folder?: number | string;
    thumbnail?: number;
    file?: any;
    actionCode?: string;
  }

  //TẢI FILE EXCEL LÊN VÀ XỬ LÝ
  interface IUploadExcelFileParams {
    ma_doi_tac_ql?: string;
    file?: any;
    actionCode?: string;
  }
  //CẬP NHẬT TÊN FILE
  interface ICapNhatFileParams {
    bt?: string | number;
    ten_alias?: string;
    actionCode?: string;
  }
  //LẤY CHI TIẾT FILE
  interface ILayChiTietFileParams {
    ma_doi_tac_ql?: string;
    bt?: string | number;
    actionCode?: string;
  }
  //XOÁ FILE
  interface IXoaFileParams {
    file?: string[] | numrber[];
    actionCode?: string;
  }
  //TẠO FOLDER MỚI
  interface ITaoFolderParams {
    id?: string | number; // nếu là update tên file -> thì truyền ID lên
    ten?: string;
    id_cha?: number | string;
    actionCode?: string;
  }
  //XOÁ FOLDER
  interface IXoaFolderParams {
    id?: number | string;
    actionCode?: string;
  }
  //TÌM KIẾM CÁC FILE TRONG FOLDER
  interface ITimKiemFolderParams {
    id?: number | string;
    ten?: string; //để tìm kiếm trong golder cha
    // actionCode?: string;
  }
  //EXPORT PDF
  interface IExportPDFParams {
    ma_doi_tac_ql?: string;
    so_id?: number | string;
    url_file?: string;
    actionCode?: string;
  }
  //EXPORT PDF HỢP ĐỒNG
  interface IExportPDFHopDongParams {
    ma_doi_tac_ql?: string;
    so_id?: number | string;
    so_id_dt?: number | string;
    template?: string;
    actionCode?: string;
  }
  //EXPORT EXCEL
  interface IExportExcelParams {
    so_id?: number | string;
    actionCode?: string;
  }
  interface IGetFileThumbnailParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    nv?: string;
  }
  interface ILietKeCauHinhPhanCapPheDuyetParams {
    nsd_duyet?: string;
    actionCode?: string;
  }
  interface ILietKeCauHinhPhanCapPheDuyetCTParams {
    bt_phan_cap?: number;
    actionCode?: string;
  }
  interface IUploadFileTheoDoiTuongXeParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    file?: Array<{
      id?: number;
    }>;
  }
  interface IUpdateCauHinhPhanCapPheDuyetParams {
    nsd_duyet?: string;
    ngay_ad?: number | string;
    bt?: number;
  }
  interface IUpdateCauHinhPhanCapPheDuyetCTParams {
    bt_phan_cap?: number;
    bt?: number | string;
    nhom_duyet?: string;
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    nv?: string;
    ma_sp?: string;
    tien_tu?: number;
    tien_toi?: number;
    trang_thai?: string;
  }
  interface IPhanLoaiFileTheoHangMucXeParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    ma_hang_muc?: string;
    file?: Array<{
      id?: number;
    }>;
  }
  interface ILietKeHangMucXeTheoNhomParams {
    nhom?: string[] | string;
  }
  interface ILuuHangMucTonThatParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    ma_hang_muc?: string;
    hm?: Array<{
      ma_hang_muc?: string;
    }>;
  }
  interface IChiTietCauHinhPhanCapPheDuyetCTParams {
    bt_phan_cap?: number;
    bt?: number;
    actionCode?: string;
  }
  interface ILuuDanhGiaTonThatParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    hm?: Array<{
      ma_hang_muc?: string;
      ma_muc_do_tt?: string;
      ghi_chu?: string;
    }>;
  }
  interface IDeleteCauHinhPhanCapPheDuyetCTParams {
    bt_phan_cap?: number;
    bt?: number;
    actionCode?: string;
    nhom_duyet?: string;
    ma_doi_tac_ql?: string;
  }
  interface ILietKeDieuKhoanNguoiDuocBaoHiemParams {
    so_id?: number | string;
    so_id_dt?: number | string;
  }

  interface ILuuDieuKhoanNguoiDuocBaoHiemParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    ad_goi_bh?: string;
    dk?: Array<{
      ma_qloi?: string;
      gh_lan_ngay?: number;
      gh_tien_lan_ngay?: number;
      gh_lan_ngay_dtri?: number;
      gh_tien_lan_ngay_dtri?: number;
      gh_tien_nam?: number;
      tgian_cho?: number;
      nt_tien_bh?: string;
      ma_qloi_tru_lui?: string;
      kieu_ad?: string;
      tl_dct?: number;
      phi_bh?: number;
      ghi_chu?: string;
    }>;
  }

  interface ILietKeDieuKhoanBoSungNguoiDuocBaoHiemParams {
    so_id?: number | string;
    so_id_dt?: number | string;
  }

  interface ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    ad_goi_bh?: string;
    dk?: Array<{
      ma_qloi?: string;
      gh_lan_ngay?: number;
      gh_tien_lan_ngay?: number;
      gh_lan_ngay_dtri?: number;
      gh_tien_lan_ngay_dtri?: number;
      gh_tien_nam?: number;
      tgian_cho?: number;
      nt_tien_bh?: string;
      ma_qloi_tru_lui?: string;
      kieu_ad?: string;
      tl_dct?: number;
      phi_bh?: number;
      ghi_chu?: string;
    }>;
  }
  interface ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams {
    ten?: string;
    ma?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    actionCode?: string;
  }
  interface IChiTietNhomPhanCapDuyetParams {
    ma?: string;
  }
  interface IUpdateNhomPhanCapDuyetParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
  }
  interface ILietKeCauHoiApDungParams {
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
  }
  interface IUpdateCauHoiApDungParams {
    ma_doi_tac_ql?: string;
    nv?: string;
    ma_sp?: string;
    ngay_ad?: number;
  }
  interface IDeleteCauHoiApDungParams {
    bt?: number;
  }
  interface ILietKeCauHoiParams {
    bt_ap_dung?: number;
  }
  interface IUpdateCauHoiParams {
    bt_ap_dung?: number;
    ma?: string;
    ten?: string;
    stt?: number;
    kieu_chon?: string;
    bat_buoc?: string;
    do_rong?: string;
    trang_thai?: string;
    ch_ct?: Array<{
      ten_gia_tri?: string;
      gia_tri?: string;
      mac_dinh?: string;
    }>;
  }
  interface IDeleteCauHoiParams {
    ma?: string;
  }
  interface IChiTietCauHoiParams {
    ma?: string;
    bt_ap_dung?: number;
  }

  interface ILietKeDanhSachNguoiPhuThuocHopDongConNguoiParams {
    so_id?: number | string;
    so_id_dt?: number | string;
  }

  interface IChiTietNguoiPhuThuocHopDongConNguoiParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    bt?: number;
  }

  interface ICapNhatNguoiPhuThuocHopDongConNguoiParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    bt?: number;
    ten?: string;
    ngay_sinh?: number | string;
    gioi_tinh?: string;
    so_cmt?: string;
    dthoai?: string;
    email?: string;
    moi_qhe?: string;
    so_hd?: string | number;
    gcn?:
      | string
      | Array<{
          so_id_dt?: number | string;
        }>;
    stt?: number;
    trang_thai?: string;
  }
  interface ILietKeDanhSachTyLeHoaHongParams {
    ma_doi_tac_ql?: string;
    ma_dai_ly?: string;
  }
  interface ICapNhatCauHinhTyLeHoaHongParams {
    ma_dai_ly?: string;
    ma_doi_tac_ql?: string;
    dl?: Array<{
      ngay_ad?: number;
      tlhh?: number;
      tlhh_ct?: number;
    }>;
  }
  interface ILayCayTyLeHoaHongParams {
    ma_doi_tac_ql?: string;
    ma_dai_ly?: string;
    ngay_ad?: number;
  }

  interface ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    // actionCode?: string;
  }
  interface IChiTietChucNangTheoVaiTroParams {
    ma?: string;
  }
  interface ICapNhatChucNangTheoVaiTroParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    stt?: number;
    ds?: Array<{
      ma?: string;
    }>;
  }
  interface IDeleteChucNangTheoVaiTroParams {
    ma?: string;
    ma_chuc_nang?: string;
  }
  interface IDeleteChucNangTheoNhomParams {
    ma?: string;
    ma_chuc_nang?: string;
  }
  interface IUpdateNhomChucNangParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    stt?: number;
    ds?: Array<{
      ma?: string;
    }>;
  }
  interface ITimKiemPhanTrangDanhSachNghiepVuParams {
    ma?: string;
    ten?: string;

    trang_thai?: string;
    trang?: number;
    so_dong?: number;
    // actionCode?: string;
  }
  interface IChiTietDanhMucNghiepVuParams {
    ma?: string;
  }
  interface IUpdateDanhMucNghiepVuParams {
    ma?: string;
    ten?: string;
    stt?: number;
    trang_thai?: string;
    actionCode?: string;
  }

  // API đánh giá sức khoẻ bảo hiểm con người - Action code: ROADJEQNJMYTA6I
  interface ILietKeCauHoiDanhGiaSucKhoeConNguoiParams {
    so_id?: number | string; // ID hợp đồng (required)
    so_id_dt?: number | string; // ID đối tượng bảo hiểm (required)
  }

  // API lưu đánh giá sức khoẻ bảo hiểm con người - Action code: GD364XH1JL8L74H
  interface ILuuDanhGiaSucKhoeConNguoiParams {
    so_id?: number | string; // ID hợp đồng (required)
    so_id_dt?: number | string; // ID đối tượng bảo hiểm (required)
    dgsk?: Array<{ma?: string; dap_an?: string}>; // Mảng chứa các object {ma: '', dap_an: ''}
  }
  interface ITimKiemPhanTrangNhomDoiTuongParams {
    ma?: string;
    ten?: string;
    trang_thai?: string;
    trang?: number;
    so_dong?: number;
  }
  interface IChiTietNhomDoiTuongParams {
    ma?: string;
  }
  interface IUpdateNhomDoiTuongParams {
    ma?: string;
    ten?: string;
    ma_cha?: string;
    mo_ta?: string;
    stt?: number;
    trang_thai?: string;
    dt_ct?: Array<{
      ma_thuoc_tinh?: string;
      ten_thuoc_tinh?: string;
      kieu_dl?: string;
    }>;
  }
  interface ITimKiemPhanTrangHopDongBaoHiemTaiSanParams {
    ngay_d?: string;
    ngay_c?: string;
    ma_doi_tac_ql?: string;
    ma_chi_nhanh_ql?: string;
    nd?: string; // ten khach hang, so hop dong
    trang_thai?: string;
    ma_sp?: string;
    ten?: string; //ten doi tuong
    trang?: number;
    so_dong?: number;
    actionCode?: string;
    nv?: string;
  }

  // CẤU HÌNH BỆNH VIỆN
  interface ITimKiemBenhVienParams extends IPhanTrang {
    id?: number; // ID gói bảo hiểm
    loai_ad?: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
    ten?: string; // Tên bệnh viện để tìm kiếm
  }

  interface ILayDanhSachBenhVienDaLuuParams {
    id?: number; // ID gói bảo hiểm
    loai_ad?: "WL" | "BL"; // Loại áp dụng
  }

  interface ILuuCauHinhBenhVienParams {
    id: number; // ID gói bảo hiểm
    loai_ad: "WL" | "BL"; // WL = Whitelist, BL = Blacklist
    bvien: Array<{
      ma: string; // mã bệnh viện
      hinh_thuc_ad: string; // hình thức áp dụng
    }>;
  }

  // CẤU HÌNH MÃ BỆNH CHO XÂY DỰNG GÓI BẢO HIỂM
  interface ITimKiemMaBenhGoiBaoHiemParams extends IPhanTrang {
    id: number; // ID gói bảo hiểm
    loai_ad: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
    ten?: string; // Tên mã bệnh để tìm kiếm
  }

  interface ILayDanhSachMaBenhDaLuuGoiBaoHiemParams {
    id: number; // ID gói bảo hiểm
    loai_ad: "WL" | "BL"; // Loại áp dụng
  }

  interface ILuuCauHinhMaBenhGoiBaoHiemParams {
    id: number; // ID gói bảo hiểm
    loai_ad: "WL" | "BL"; // WL = Whitelist, BL = Blacklist
    benh: Array<{
      ma: string; // mã bệnh
      hinh_thuc_ad: string; // hình thức áp dụng
    }>;
  }
  interface IDeleteNhomDoiTuongCTParams {
    ma_thuoc_tinh?: string;
    ma_nhom_dt?: string;
  }
  // CẤU HÌNH BỆNH VIỆN CHO HỢP ĐỒNG CON NGƯỜI
  interface ITimKiemBenhVienHopDongConNguoiParams extends IPhanTrang {
    so_id: number | string; // ID hợp đồng
    so_id_dt: number | string; // ID đối tượng được bảo hiểm
    loai_ad: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
    ten?: string; // Tên bệnh viện để tìm kiếm
  }

  interface ILayDanhSachBenhVienDaLuuHopDongConNguoiParams {
    so_id: number | string; // ID hợp đồng
    so_id_dt: number | string; // ID đối tượng được bảo hiểm
    loai_ad: "WL" | "BL"; // Loại áp dụng
  }

  interface ILuuCauHinhBenhVienHopDongConNguoiParams {
    so_id: number | string; // ID hợp đồng
    so_id_dt: number | string; // ID đối tượng được bảo hiểm
    loai_ad: "WL" | "BL"; // WL = Whitelist, BL = Blacklist
    bvien: Array<{
      ma: string; // mã bệnh viện
      hinh_thuc_ad: string; // hình thức áp dụng
    }>;
  }

  // CẤU HÌNH MÃ BỆNH CHO HỢP ĐỒNG CON NGƯỜI
  interface ITimKiemMaBenhHopDongConNguoiParams extends IPhanTrang {
    so_id: number | string; // ID hợp đồng
    so_id_dt: number | string; // ID đối tượng được bảo hiểm
    loai_ad: "WL" | "BL"; // Loại áp dụng: WL = Whitelist, BL = Blacklist
    ten?: string; // Tên mã bệnh để tìm kiếm
  }

  interface ILayDanhSachMaBenhDaLuuHopDongConNguoiParams {
    so_id: number | string; // ID hợp đồng
    so_id_dt: number | string; // ID đối tượng được bảo hiểm
    loai_ad: "WL" | "BL"; // Loại áp dụng
  }

  interface ILuuCauHinhMaBenhHopDongConNguoiParams {
    so_id: number | string; // ID hợp đồng
    so_id_dt: number | string; // ID đối tượng được bảo hiểm
    loai_ad: "WL" | "BL"; // WL = Whitelist, BL = Blacklist
    benh: Array<{
      ma: string; // mã bệnh
      hinh_thuc_ad: string; // hình thức áp dụng
    }>;
  }
  interface IChiTietHopDongTaiSanParams {
    so_id?: number | string;
    ma_doi_tac_ql?: string;
  }
  interface ICapNhatDoiTuongBaoHiemTaiSanParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    ten?: string;
    gcn?:
      | string
      | Array<{
          so_id_dt?: number | string;
          tl_dong?: number;
        }>;
    nhom_dt?: string;
    vi_tri?: string;
    tinh_thanh?: string;
    phuong_xa?: string;
    dchi?: string;
    latitude?: number;
    longitude?: number;
    gia_tri?: number;
    gio_hl?: string;
    ngay_hl?: string | number;
    gio_kt?: string;
    ngay_kt?: string | number;
    ngay_cap?: string | number;
    vip?: string;
    doi_tuong_ttu_rr?: string;
    // dk?: Array<string>;
    // dkbs?: Array<{
    //   ma_qloi?: string;
    //   ten_qloi?: string;
    //   ma_qloi_ct?: string;
    //   gh_lan_ngay?: number;
    //   gh_tien_lan_ngay?: number;
    //   gh_lan_ngay_dtri?: number;
    //   gh_tien_lan_ngay_dtri?: number;
    //   gh_tien_nam?: number;
    //   tgian_cho?: number;
    //   nt_tien_bh?: string;
    //   ma_qloi_tru_lui?: string;
    //   kieu_ad?: string;
    //   tl_dct?: number;
    //   phi_bh?: number;
    //   ghi_chu?: string;
    // }>;
    ttinh?: Array<{
      ma?: string;
      gia_tri?: string;
    }>;
  }
  interface IChiTietDoiTuongBaoHiemTaiSanParams {
    so_id?: number | string;
    so_id_dt?: number | string;
  }
  interface ILietKeDanhSachPhuongXaParams {
    ma_tinh?: string;
  }
  interface ITimKiemPhanTrangDanhSachTaiSanParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    nd_tim?: string;
    trang?: number;
    so_dong?: number;
  }
  interface ILayChiTietDanhSachTaiSanParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    so_id_ts?: number | string;
  }
  interface ICapNhatDanhSachTaiSanParams {
    so_id?: number | string;
    so_id_dt?: number | string;
    so_id_ts?: number | string;
    ten?: string;
    so_luong?: number;
    don_gia?: number;
    tong_tien?: number;
    nam_sx?: number;
    han_sd?: number;
  }
  interface ITimKiemPhanTrangDoiTuongParams {
    ten?: string;
    trang?: number;
    so_dong?: number;
  }
  interface IChiTietDoiTuongParams {
    ma?: string;
  }
  interface IUpdateDoiTuongParams {
    ma?: string;
    ten?: string;
    tinh_thanh?: string;
    phuong_xa?: string;
    dchi?: string;
    latitude?: number;
    longitude?: number;
    // trang_thai?: string;
    // actionCode?: string;
  }
}
