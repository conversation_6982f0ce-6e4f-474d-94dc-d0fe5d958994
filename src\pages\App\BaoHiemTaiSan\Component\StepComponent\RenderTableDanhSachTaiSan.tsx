import {useBaoHiemTaiSanContext} from "../../index.context";
import {Form, Row, Col, Table, Flex, Tag, TableColumnType, Input, Tooltip, InputRef} from "antd";
import {useState, useMemo, useRef} from "react";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import {DataIndexTaiSan, FormTimKiemDanhSachTaiSan, IModalchiTietDanhSachTaiSanRef, radioItemTrangThaiTaiSanTable, TableTaiSanColumnDataType} from "./Constant";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {COLOR_PALETTE} from "@src/constants";
import {ModalchiTietDanhSachTaiSan} from "./ModalChiTietTaiSan";

interface RenderTableDanhSachTaiSanProps {
  columns: any[];
  doiTuongTaiSanSelected: any;
}

export function RenderTableDanhSachTaiSan({columns, doiTuongTaiSanSelected}: RenderTableDanhSachTaiSanProps) {
  const {danhSachTaiSan, loading, tongSoDongDanhSachTaiSan, timKiemPhanTrangDanhSachTaiSan, layChiTietDanhSachTaiSan, chiTietHopDongBaoHiemTaiSan} = useBaoHiemTaiSanContext();
  const {nd_tim} = FormTimKiemDanhSachTaiSan;
  const [formTimKiem] = Form.useForm();
  const [searchParams, setSearchParams] = useState<any>({nd_tim: ""});
  const [page, setPage] = useState(1);
  const [doiTuongXeSelected, setDoiTuongXeSelected] = useState<any>(null);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const refModalChiTietTaiSan = useRef<IModalchiTietDanhSachTaiSanRef>(null);
  //form
  const [formTimKiemDanhSachTaiSan] = Form.useForm();
  //   useEffect(() => {
  //     const pageSizeProp = 10;
  //     function calculatePageSize() {
  //       // Giả sử header, search form, ... chiếm 300px, mỗi dòng table 48px
  //       const availableHeight = window.innerHeight - 300;
  //       const rowHeight = 48;
  //       const calculatedPageSize = Math.max(3, Math.floor(availableHeight / rowHeight)); // tối thiểu 3 dòng
  //       setPageSize(calculatedPageSize);
  //     }
  //     calculatePageSize();
  //     window.addEventListener("resize", calculatePageSize);
  //     return () => window.removeEventListener("resize", calculatePageSize);
  //   }, []);

  // DATA TABLE
  const dataTableListTaiSan = useMemo(() => {
    try {
      const tableData = danhSachTaiSan.map((item: any, index: number) => ({
        ...item,
        // han_sd: item.han_sd.format("DD/MM/YYYY"),
        so_id: chiTietHopDongBaoHiemTaiSan?.so_id,
        so_id_dt: doiTuongTaiSanSelected?.so_id_dt,
        key: index.toString(),
      }));
      if (tableData.length > 0 && !doiTuongXeSelected) setDoiTuongXeSelected(tableData[0]);
      const arrEmptyRow: Array<TableTaiSanColumnDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [danhSachTaiSan, pageSize]);
  //nhập ở ô input tìm kiếm tự động gọi API
  //   const deboundSearch = useMemo(
  //     () =>
  //       debounce(event => {
  //         const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
  //         const params = {
  //           so_id,
  //           nd_tim: event.target.value ?? "",
  //           so_id_dt: chiTietDoiTuongBaoHiemTaiSan?.so_id_dt,
  //           //   so_id_ts: 0,
  //         };
  //         setSearchParams(params);
  //         timKiemPhanTrangDanhSachTaiSan({...params, trang: 1, so_dong: pageSize});
  //       }, 500),
  //     [],
  //   );

  //   // Tìm kiếm và phân trang
  //   const handleSearchAndPagination = useCallback(
  //     (values?: any, pageArg?: number) => {
  //       const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
  //       if (values) {
  //         const cleanedValues = {
  //           ...values,
  //           nd_tim: values.nd_tim ?? "",
  //           so_id,
  //         };
  //         setSearchParams(cleanedValues);
  //         setPage(1);
  //         timKiemPhanTrangDanhSachTaiSan({...cleanedValues, trang: 1, so_dong: pageSize});
  //       } else {
  //         const page = pageArg || 1;
  //         setPage(page);
  //         timKiemPhanTrangDanhSachTaiSan({
  //           ...searchParams,
  //           so_id,
  //           trang: page,
  //           so_dong: pageSize,
  //         });
  //       }
  //     },
  //     [chiTietHopDongBaoHiemTaiSan, searchParams, pageSize],
  //   );
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangDanhSachTaiSanParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      nd_tim: values.nd_tim ?? "",
      so_id_dt: doiTuongTaiSanSelected?.gcn.so_id_dt,
      so_id: chiTietHopDongBaoHiemTaiSan?.so_id,
    };
    setSearchParams(cleanedValues);
    timKiemPhanTrangDanhSachTaiSan({...cleanedValues, trang: 1, so_dong: pageSize});
    // setFilterParams({...cleanedValues, trang: cleanedValues.trang, so_dong: defaultPaginationTableProps.defaultPageSize});
  };
  const handleSearch = (selectedKeys: string[], confirm: any, dataIndex: DataIndexTaiSan) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };
  const handleReset = (clearFilters: any, confirm: any, dataIndex: DataIndexTaiSan) => {
    clearFilters();
    setSearchText("");
    setSearchedColumn(dataIndex);
  };
  const renderFormInputColum = (props?: any, span = 6) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndexTaiSan, title: string): TableColumnType<TableTaiSanColumnDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiTaiSanTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  // Header tìm kiếm
  const renderHeaderTable = () => {
    return (
      <Form form={formTimKiemDanhSachTaiSan} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
        <div className="flex flex-col gap-5">
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...nd_tim})}

            <Col span={3}>
              <Form.Item>
                <Flex wrap="wrap" gap="small" className="w-">
                  <Button className="w-full" htmlType="submit" type="primary" icon={<SearchOutlined />} loading={loading}>
                    Tìm kiếm
                  </Button>
                </Flex>
              </Form.Item>
            </Col>

            <Col span={3}>
              <Form.Item>
                <Flex wrap="wrap" gap="small" className="">
                  <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietTaiSan.current?.open()} loading={loading}>
                    Tạo mới
                  </Button>
                </Flex>
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Form>
    );
  };

  // Summary mặc định
  //   const defaultSummary = () => (
  //     <Table.Summary fixed>
  //       <Table.Summary.Row>
  //         <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
  //           <div className="text-center font-medium">Tổng phí bảo hiểm</div>
  //         </Table.Summary.Cell>
  //         <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
  //           <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiemTaiSanFromAPI)}</div>
  //         </Table.Summary.Cell>
  //       </Table.Summary.Row>
  //     </Table.Summary>
  //   );

  return (
    <div>
      <Table<TableTaiSanColumnDataType>
        className="custom-table-title-padding"
        //   style={style}
        {...defaultTableProps}
        dataSource={dataTableListTaiSan}
        columns={(columns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableTaiSanColumnDataType, item.title) : {}),
          };
        })}
        loading={loading}
        sticky
        //   rowClassName={(record: any) => (record.so_id_dt && record.so_id_dt === doiTuongXeSelected?.so_id_dt ? "table-row-active" : "")}
        pagination={{
          size: "small",
          ...defaultPaginationTableProps,
          total: tongSoDongDanhSachTaiSan,
          pageSize: pageSize,
          current: page,
          // onChange: (page: any) => handleSearchAndPagination(undefined, page),
          locale: {
            jump_to: "Tới trang",
            page: "",
          },
        }}
        showHeader={true}
        title={renderHeaderTable}
        bordered
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietDanhSachTaiSan = await layChiTietDanhSachTaiSan({so_id: record.so_id, so_id_dt: doiTuongTaiSanSelected?.gcn?.so_id_dt, so_id_ts: record.so_id_ts});
              console.log("chiTietQuyenLoi", chiTietDanhSachTaiSan);
              if (chiTietDanhSachTaiSan) {
                refModalChiTietTaiSan.current?.open(chiTietDanhSachTaiSan);
              }
            },
          };
        }}
        //   summary={summaryRender ? () => summaryRender(tongPhiBaoHiemTaiSanFromAPI) : defaultSummary}
      />
      <ModalchiTietDanhSachTaiSan ref={refModalChiTietTaiSan} doiTuongTaiSanSelected={doiTuongTaiSanSelected} />
    </div>
  );
}
